import 'package:flutter/material.dart';

/// Icon definitions and utilities for the Upshift app
/// Following the mixed iconography style (outlined for navigation/actions, filled for status/achievements)
class AppIcons {
  // Navigation Icons (Outlined)
  static const IconData home = Icons.home_outlined;
  static const IconData homeSelected = Icons.home;
  static const IconData chat = Icons.chat_bubble_outline;
  static const IconData chatSelected = Icons.chat_bubble;
  static const IconData paths = Icons.map_outlined;
  static const IconData pathsSelected = Icons.map;
  static const IconData profile = Icons.person_outline;
  static const IconData profileSelected = Icons.person;
  static const IconData admin = Icons.admin_panel_settings_outlined;
  static const IconData adminSelected = Icons.admin_panel_settings;
  static const IconData lifeWheel = Icons.donut_large_outlined;
  static const IconData lifeWheelSelected = Icons.donut_large;

  // Action Icons (Outlined)
  static const IconData add = Icons.add;
  static const IconData edit = Icons.edit_outlined;
  static const IconData delete = Icons.delete_outline;
  static const IconData share = Icons.share_outlined;
  static const IconData more = Icons.more_vert;
  static const IconData search = Icons.search;
  static const IconData filter = Icons.filter_list_outlined;
  static const IconData sort = Icons.sort;
  static const IconData refresh = Icons.refresh;
  static const IconData settings = Icons.settings_outlined;
  static const IconData help = Icons.help_outline;
  static const IconData info = Icons.info_outline;
  static const IconData close = Icons.close;
  static const IconData back = Icons.arrow_back;
  static const IconData forward = Icons.arrow_forward;
  static const IconData up = Icons.keyboard_arrow_up;
  static const IconData down = Icons.keyboard_arrow_down;
  static const IconData left = Icons.keyboard_arrow_left;
  static const IconData right = Icons.keyboard_arrow_right;

  // Status Icons (Filled)
  static const IconData success = Icons.check_circle;
  static const IconData error = Icons.error;
  static const IconData warning = Icons.warning;
  static const IconData infoFilled = Icons.info;
  static const IconData completed = Icons.check_circle;
  static const IconData inProgress = Icons.radio_button_checked;
  static const IconData notStarted = Icons.radio_button_unchecked;
  static const IconData locked = Icons.lock;
  static const IconData unlocked = Icons.lock_open;

  // Achievement Icons (Filled)
  static const IconData trophy = Icons.emoji_events;
  static const IconData medal = Icons.military_tech;
  static const IconData star = Icons.star;
  static const IconData starOutline = Icons.star_border;
  static const IconData starHalf = Icons.star_half;
  static const IconData badge = Icons.verified;
  static const IconData crown = Icons.workspace_premium;
  static const IconData diamond = Icons.diamond;

  // Category Icons
  static const IconData focusProductivity = Icons.work_outline;
  static const IconData mindsetResilience = Icons.psychology_outlined;
  static const IconData habitFormation = Icons.repeat;
  static const IconData lifeDesign = Icons.design_services_outlined;

  // Difficulty Icons
  static const IconData beginner = Icons.star_border;
  static const IconData intermediate = Icons.star_half;
  static const IconData advanced = Icons.star;

  // Chat Icons
  static const IconData send = Icons.send;
  static const IconData attach = Icons.attach_file;
  static const IconData mic = Icons.mic;
  static const IconData micOff = Icons.mic_off;
  static const IconData image = Icons.image_outlined;
  static const IconData camera = Icons.camera_alt_outlined;
  static const IconData emoji = Icons.emoji_emotions_outlined;

  // Progress Icons
  static const IconData progress = Icons.trending_up;
  static const IconData target = Icons.track_changes;
  static const IconData calendar = Icons.calendar_today_outlined;
  static const IconData timer = Icons.timer_outlined;
  static const IconData streak = Icons.local_fire_department;

  // User Icons
  static const IconData user = Icons.person_outline;
  static const IconData users = Icons.people_outline;
  static const IconData userAdd = Icons.person_add_outlined;
  static const IconData userRemove = Icons.person_remove_outlined;

  // Email Verification Icons
  static const IconData email = Icons.email_outlined;
  static const IconData emailVerified = Icons.mark_email_read;
  static const IconData emailUnverified = Icons.mark_email_unread_outlined;
  static const IconData emailSend = Icons.outgoing_mail;
  static const IconData emailResend = Icons.refresh;
  static const IconData emailCheck = Icons.check_circle_outline;

  // System Icons
  static const IconData notification = Icons.notifications_outlined;
  static const IconData notificationActive = Icons.notifications;
  static const IconData darkMode = Icons.dark_mode_outlined;
  static const IconData lightMode = Icons.light_mode_outlined;
  static const IconData language = Icons.language;
  static const IconData security = Icons.security_outlined;
  static const IconData privacy = Icons.privacy_tip_outlined;

  // Content Icons
  static const IconData text = Icons.text_fields;
  static const IconData quote = Icons.format_quote;
  static const IconData list = Icons.list;
  static const IconData grid = Icons.grid_view;
  static const IconData bookmark = Icons.bookmark_outline;
  static const IconData bookmarkFilled = Icons.bookmark;
  static const IconData favorite = Icons.favorite_outline;
  static const IconData favoriteFilled = Icons.favorite;

  // Navigation Flow Icons
  static const IconData next = Icons.navigate_next;
  static const IconData previous = Icons.navigate_before;
  static const IconData skip = Icons.skip_next;
  static const IconData replay = Icons.replay;

  /// Get category icon by name
  static IconData getCategoryIcon(String category) {
    switch (category) {
      case 'Focus & Productivity':
        return focusProductivity;
      case 'Mindset & Resilience':
        return mindsetResilience;
      case 'Habit Formation':
        return habitFormation;
      case 'Life Design':
        return lifeDesign;
      default:
        return help;
    }
  }

  /// Get difficulty icon by level
  static IconData getDifficultyIcon(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return beginner;
      case 'intermediate':
        return intermediate;
      case 'advanced':
        return advanced;
      default:
        return help;
    }
  }

  /// Get status icon by state
  static IconData getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'success':
        return completed;
      case 'in_progress':
      case 'current':
        return inProgress;
      case 'not_started':
      case 'pending':
        return notStarted;
      case 'locked':
      case 'disabled':
        return locked;
      case 'error':
      case 'failed':
        return error;
      case 'warning':
        return warning;
      default:
        return help;
    }
  }

  /// Get navigation icon (with selected state)
  static IconData getNavigationIcon(String page, {bool selected = false}) {
    switch (page.toLowerCase()) {
      case 'home':
        return selected ? homeSelected : home;
      case 'chat':
        return selected ? chatSelected : chat;
      case 'paths':
        return selected ? pathsSelected : paths;
      case 'profile':
        return selected ? profileSelected : profile;
      case 'admin':
        return selected ? adminSelected : admin;
      case 'lifewheel':
      case 'life_wheel':
        return selected ? lifeWheelSelected : lifeWheel;
      default:
        return help;
    }
  }

  /// Get achievement icon by type
  static IconData getAchievementIcon(String type) {
    switch (type.toLowerCase()) {
      case 'trophy':
        return trophy;
      case 'medal':
        return medal;
      case 'star':
        return star;
      case 'badge':
        return badge;
      case 'crown':
        return crown;
      case 'diamond':
        return diamond;
      default:
        return badge;
    }
  }

  /// Create an icon with specific size and color
  static Widget icon(
    IconData iconData, {
    double? size,
    Color? color,
    String? semanticLabel,
  }) {
    return Icon(
      iconData,
      size: size ?? 24.0,
      color: color,
      semanticLabel: semanticLabel,
    );
  }

  /// Create a circular icon with background
  static Widget circularIcon(
    IconData iconData, {
    double size = 40.0,
    Color? iconColor,
    Color? backgroundColor,
    double iconSize = 20.0,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(color: backgroundColor, shape: BoxShape.circle),
      child: Icon(iconData, size: iconSize, color: iconColor),
    );
  }

  /// Create an icon button with consistent styling
  static Widget iconButton(
    IconData iconData, {
    required VoidCallback onPressed,
    double size = 24.0,
    Color? color,
    String? tooltip,
    EdgeInsetsGeometry? padding,
  }) {
    return IconButton(
      onPressed: onPressed,
      icon: Icon(iconData, size: size, color: color),
      tooltip: tooltip,
      padding: padding ?? const EdgeInsets.all(8.0),
    );
  }
}
