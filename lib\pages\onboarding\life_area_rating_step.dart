import 'package:flutter/material.dart';
import '../../widgets/onboarding/onboarding_step_base.dart';
import '../../widgets/life_wheel/life_wheel_widgets.dart';

class LifeAreaRatingStep extends StatefulWidget {
  final String areaName;
  final String areaDescription;
  final void Function(int rating) onRatingChanged;
  final VoidCallback? onBack;
  final VoidCallback? onSkip;
  final VoidCallback? onContinue;
  final int? initialRating;
  final int progressCurrent;
  final int progressTotal;

  const LifeAreaRatingStep({
    super.key,
    required this.areaName,
    required this.areaDescription,
    required this.onRatingChanged,
    this.onBack,
    this.onSkip,
    this.onContinue,
    this.initialRating,
    required this.progressCurrent,
    required this.progressTotal,
  });

  @override
  State<LifeAreaRatingStep> createState() => _LifeAreaRatingStepState();
}

class _LifeAreaRatingStepState extends State<LifeAreaRatingStep> {
  int? _rating;

  @override
  void initState() {
    super.initState();
    _rating = widget.initialRating;
  }

  void _selectRating(int value) {
    setState(() => _rating = value);
    widget.onRatingChanged(value);
  }

  @override
  Widget build(BuildContext context) {
    return OnboardingStepBase(
      currentStep: widget.progressCurrent,
      totalSteps: widget.progressTotal,
      title: widget.areaName,
      subtitle: widget.areaDescription,
      onBack: widget.onBack,
      onSkip: null,
      onContinue: _rating != null ? widget.onContinue : null,
      canContinue: _rating != null,
      child: LifeAreaRatingWidget(
        areaName: widget.areaName,
        areaDescription: widget.areaDescription,
        initialRating: _rating,
        onRatingChanged: _selectRating,
        showDescription: false, // Already shown in OnboardingStepBase
      ),
    );
  }
}
