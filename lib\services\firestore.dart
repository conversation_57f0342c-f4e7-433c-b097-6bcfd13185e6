import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../models/models.dart';
import 'logging_service.dart';
import 'performance_service.dart';
import 'subscription_service.dart';

class FirestoreService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final auth.FirebaseAuth _firebaseAuth = auth.FirebaseAuth.instance;
  static const _uuid = Uuid();

  // Get user by userId
  static Future<User?> getUser(String userId) async {
    return PerformanceService.instance.measureFirestoreOperation(
      () async {
        try {
          LoggingService.instance.logInfo(
            'FirestoreService',
            'Getting user: $userId',
          );

          final docSnapshot = await _firestore
              .collection('users')
              .doc(userId)
              .get();

          if (docSnapshot.exists && docSnapshot.data() != null) {
            LoggingService.instance.logInfo(
              'FirestoreService',
              'User found: $userId',
            );
            return User.fromJson(docSnapshot.data()!);
          }

          LoggingService.instance.logInfo(
            'FirestoreService',
            'User not found: $userId',
          );
          return null;
        } catch (e, stackTrace) {
          await LoggingService.instance.logError(
            e,
            stackTrace,
            'FirestoreService',
            'Failed to get user: $userId',
          );
          throw Exception('Failed to get user: $e');
        }
      },
      'get',
      'users',
    );
  }

  // Get all chats by ownerId (user uuid)
  static Future<List<Chat>> getChats(String ownerId) async {
    try {
      final querySnapshot = await _firestore
          .collection('chats')
          .where('ownerId', isEqualTo: ownerId)
          .orderBy('lastUpdatedDate', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id; // Add the document ID to the data
        return Chat.fromJson(data);
      }).toList();
    } catch (e) {
      throw Exception('Failed to get chats: $e');
    }
  }

  // Get specific chat by ownerId and chatId
  static Future<Chat?> getChat(String ownerId, String chatId) async {
    try {
      final docSnapshot = await _firestore
          .collection('chats')
          .doc(chatId)
          .get();

      if (docSnapshot.exists && docSnapshot.data() != null) {
        final data = docSnapshot.data()!;
        data['id'] = docSnapshot.id; // Add the document ID to the data
        final chat = Chat.fromJson(data);

        // Verify that the chat belongs to the specified owner
        if (chat.ownerId == ownerId) {
          return chat;
        } else {
          throw Exception('Chat does not belong to the specified owner');
        }
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get chat: $e');
    }
  }

  // Create a new chat and persist it to Firestore
  static Future<String> createChat(
    String ownerId, {
    String title = "New Chat",
    required String systemPersonaId,
    PathStep? pathStep,
    String? chatSubjectId,
  }) async {
    return PerformanceService.instance.measureChatCreation(() async {
      try {
        LoggingService.instance.logInfo(
          'FirestoreService',
          'Creating chat for user: $ownerId with persona: $systemPersonaId',
        );

        final chatId = _uuid.v4();
        final now = DateTime.now();

        final chat = Chat(
          title: title,
          ownerId: ownerId,
          systemPersonaId: systemPersonaId,
          startedDate: now,
          lastUpdatedDate: now,
          isCompleted: false,
          archived: false,
          pathStepId: pathStep?.id,
          chatSubjectId: chatSubjectId,
          isTitleLocked: pathStep != null ? true : null,
          metadata: {},
        );

        await _firestore.collection('chats').doc(chatId).set(chat.toJson());

        LoggingService.instance.logInfo(
          'FirestoreService',
          'Chat created successfully: $chatId',
        );

        return chatId;
      } catch (e, stackTrace) {
        await LoggingService.instance.logError(
          e,
          stackTrace,
          'FirestoreService',
          'Failed to create chat for user: $ownerId',
        );
        throw Exception('Failed to create chat: $e');
      }
    }, systemPersonaId);
  }

  // Get all messages for a specific chat
  static Future<List<Message>> getMessages(
    String chatId,
    String ownerId,
  ) async {
    try {
      final querySnapshot = await _firestore
          .collection('chats')
          .doc(chatId)
          .collection('messages')
          .where('chatOwnerId', isEqualTo: ownerId)
          .orderBy('postedDate', descending: false)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id; // Add the document ID to the data
        return Message.fromJson(data);
      }).toList();
    } catch (e) {
      throw Exception('Failed to get messages: $e');
    }
  }

  // Save a message to Firestore
  static Future<String> saveMessage(Message message) async {
    try {
      final messageId = _uuid.v4();
      final messageData = message.toJson();

      await _firestore
          .collection('chats')
          .doc(message.chatId)
          .collection('messages')
          .doc(messageId)
          .set(messageData);

      // Update the chat's lastUpdatedDate
      await _firestore.collection('chats').doc(message.chatId).update({
        'lastUpdatedDate': Timestamp.fromDate(message.postedDate),
      });

      return messageId;
    } catch (e) {
      throw Exception('Failed to save message: $e');
    }
  }

  // Create a Message object from flutter_chat_core TextMessage
  static Message createMessageFromText({
    required String chatId,
    required String chatOwnerId,
    required String? userId,
    String? systemPersonaId,
    required String textContent,
    required DateTime postedDate,
    Map<String, dynamic>? metadata,
  }) {
    return Message(
      chatId: chatId,
      chatOwnerId: chatOwnerId,
      userId: userId,
      systemPersonaId: systemPersonaId,
      postedDate: postedDate,
      isDeleted: false,
      type: 'text',
      textContent: textContent,
      imageUrl: null,
      reactionCounts: {},
      edited: false,
      editDate: null,
      replyToMessageId: null,
      metadata: metadata,
    );
  }

  // Create a Message object from flutter_chat_core ImageMessage
  static Message createMessageFromImage({
    required String chatId,
    required String chatOwnerId,
    required String? userId,
    String? systemPersonaId,
    required String imageUrl,
    required DateTime postedDate,
    Map<String, dynamic>? metadata,
  }) {
    return Message(
      chatId: chatId,
      chatOwnerId: chatOwnerId,
      userId: userId,
      systemPersonaId: systemPersonaId,
      postedDate: postedDate,
      isDeleted: false,
      type: 'image',
      textContent: null,
      imageUrl: imageUrl,
      reactionCounts: {},
      edited: false,
      editDate: null,
      replyToMessageId: null,
      metadata: metadata,
    );
  }

  // Update chat title
  static Future<void> updateChatTitle(String chatId, String newTitle) async {
    try {
      await _firestore.collection('chats').doc(chatId).update({
        'title': newTitle,
        'lastUpdatedDate': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('Failed to update chat title: $e');
    }
  }

  // Update chat completion status
  static Future<void> updateChatCompletion(
    String chatId,
    bool isCompleted,
  ) async {
    try {
      await _firestore.collection('chats').doc(chatId).update({
        'isCompleted': isCompleted,
        'lastUpdatedDate': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('Failed to update chat completion: $e');
    }
  }

  // Delete a chat and all its messages
  static Future<void> deleteChat(String chatId, String ownerId) async {
    return PerformanceService.instance.measureFirestoreOperation(
      () async {
        try {
          LoggingService.instance.logInfo(
            'FirestoreService',
            'Deleting chat: $chatId for owner: $ownerId',
          );

          // First verify the chat belongs to the owner
          final chat = await getChat(ownerId, chatId);
          if (chat == null) {
            throw Exception('Chat not found or does not belong to owner');
          }

          // Get all messages in the chat
          final messages = await getMessages(chatId, ownerId);

          // Delete all messages in batches
          if (messages.isNotEmpty) {
            final batch = _firestore.batch();
            for (final message in messages) {
              if (message.id != null) {
                batch.delete(
                  _firestore
                      .collection('chats')
                      .doc(chatId)
                      .collection('messages')
                      .doc(message.id!),
                );
              }
            }
            await batch.commit();
          }

          // Delete the chat document
          await _firestore.collection('chats').doc(chatId).delete();

          LoggingService.instance.logInfo(
            'FirestoreService',
            'Chat deleted successfully: $chatId',
          );
        } catch (e, stackTrace) {
          await LoggingService.instance.logError(
            e,
            stackTrace,
            'FirestoreService',
            'Failed to delete chat: $chatId for owner: $ownerId',
          );
          throw Exception('Failed to delete chat: $e');
        }
      },
      'delete',
      'chats',
    );
  }

  // Create or update user in Firestore
  static Future<void> createOrUpdateUser(User user) async {
    try {
      await _firestore.collection('users').doc(user.id).set(user.toJson());
    } catch (e) {
      throw Exception('Failed to create/update user: $e');
    }
  }

  // Update user onboarding status and related fields
  static Future<void> updateUserOnboarding({
    required String userId,
    required String name,
    String? description,
    required List<String> preferredPersonaIds,
  }) async {
    try {
      // Update Firestore User document
      await _firestore.collection('users').doc(userId).update({
        'name': name,
        'description': description,
        'preferredPersonaIds': preferredPersonaIds,
        'isOnboarded': true,
      });

      // Update Firebase Auth displayName to keep it in sync
      final currentUser = _firebaseAuth.currentUser;
      if (currentUser != null && currentUser.uid == userId) {
        await currentUser.updateDisplayName(name);
      }
    } catch (e) {
      throw Exception('Failed to update user onboarding: $e');
    }
  }

  // Update user admin status (for admin management)
  static Future<void> updateUserAdminStatus({
    required String userId,
    required bool isAdmin,
  }) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'isAdmin': isAdmin,
      });
    } catch (e) {
      throw Exception('Failed to update user admin status: $e');
    }
  }

  // Update user name across all three data stores atomically
  static Future<void> updateUserName({
    required String userId,
    required String newName,
  }) async {
    if (newName.trim().isEmpty) {
      throw Exception('Name cannot be empty');
    }

    final trimmedName = newName.trim();

    // Store original values for rollback
    String? originalFirebaseDisplayName;
    String? originalFirestoreUserName;
    String? originalUserProfileName;

    try {
      // Get current Firebase Auth user
      final currentUser = _firebaseAuth.currentUser;
      if (currentUser == null || currentUser.uid != userId) {
        throw Exception('User not authenticated or user ID mismatch');
      }

      // Store original Firebase Auth displayName
      originalFirebaseDisplayName = currentUser.displayName;

      // Get current Firestore User document
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        throw Exception('User document not found in Firestore');
      }

      final userData = userDoc.data()!;
      originalFirestoreUserName = userData['name'] as String?;

      // Get current UserProfile document (if it exists)
      final userProfileDoc = await _firestore
          .collection('userProfiles')
          .doc(userId)
          .get();
      UserProfile? currentUserProfile;
      if (userProfileDoc.exists) {
        currentUserProfile = UserProfile.fromJson(userProfileDoc.data()!);
        originalUserProfileName = currentUserProfile.name;
      }

      // Step 1: Update Firebase Auth displayName
      await currentUser.updateDisplayName(trimmedName);

      // Step 2: Update Firestore User document
      await _firestore.collection('users').doc(userId).update({
        'name': trimmedName,
      });

      // Step 3: Update UserProfile document if it exists
      if (currentUserProfile != null) {
        await _firestore.collection('userProfiles').doc(userId).update({
          'name': trimmedName,
        });
      }
    } catch (e) {
      // Rollback changes if any step failed
      try {
        final currentUser = _firebaseAuth.currentUser;

        // Rollback Firebase Auth displayName
        if (currentUser != null && originalFirebaseDisplayName != null) {
          await currentUser.updateDisplayName(originalFirebaseDisplayName);
        }

        // Rollback Firestore User document
        if (originalFirestoreUserName != null) {
          await _firestore.collection('users').doc(userId).update({
            'name': originalFirestoreUserName,
          });
        }

        // Rollback UserProfile document
        if (originalUserProfileName != null) {
          await _firestore.collection('userProfiles').doc(userId).update({
            'name': originalUserProfileName,
          });
        }
      } catch (rollbackError) {
        // Log rollback error but don't throw it
        debugPrint('Failed to rollback user name update: $rollbackError');
      }

      throw Exception('Failed to update user name: $e');
    }
  }

  // ===== NOTIFICATION METHODS =====

  /// Update user notification preferences
  static Future<void> updateUserNotificationPreferences({
    required String userId,
    required NotificationPreferences preferences,
  }) async {
    try {
      LoggingService.instance.logInfo(
        'FirestoreService',
        'Updating notification preferences for user: $userId',
      );

      await _firestore.collection('users').doc(userId).update({
        'notificationPreferences': preferences.toJson(),
      });

      LoggingService.instance.logInfo(
        'FirestoreService',
        'Successfully updated notification preferences for user: $userId',
      );
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        'FirestoreService',
        'Failed to update notification preferences for user: $userId',
      );
      throw Exception('Failed to update notification preferences: $e');
    }
  }

  /// Update FCM token for a user
  static Future<void> updateUserFCMToken({
    required String userId,
    required String deviceId,
    required String fcmToken,
  }) async {
    await updateUserDeviceFCMToken(
      userId: userId,
      deviceId: deviceId,
      fcmToken: fcmToken,
    );
  }

  /// Update FCM token for a specific device
  static Future<void> updateUserDeviceFCMToken({
    required String userId,
    required String deviceId,
    required String fcmToken,
  }) async {
    try {
      LoggingService.instance.logInfo(
        'FirestoreService',
        'Updating FCM token for user: $userId, device: $deviceId',
      );

      // Get current user to preserve existing notification preferences
      final user = await getUser(userId);
      if (user == null) {
        throw Exception('User not found');
      }

      // Update or create notification preferences with new device token
      final currentPreferences =
          user.notificationPreferences ??
          NotificationPreferences.defaultPreferences();

      final updatedPreferences = currentPreferences.setDeviceToken(
        deviceId,
        fcmToken,
      );

      await _firestore.collection('users').doc(userId).update({
        'notificationPreferences': updatedPreferences.toJson(),
      });

      LoggingService.instance.logInfo(
        'FirestoreService',
        'Successfully updated FCM token for user: $userId, device: $deviceId',
      );
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        'FirestoreService',
        'Failed to update FCM token for user: $userId, device: $deviceId',
      );
      throw Exception('Failed to update FCM token: $e');
    }
  }

  /// Remove FCM token for a specific device
  static Future<void> removeUserDeviceFCMToken({
    required String userId,
    required String deviceId,
  }) async {
    try {
      LoggingService.instance.logInfo(
        'FirestoreService',
        'Removing FCM token for user: $userId, device: $deviceId',
      );

      // Get current user
      final user = await getUser(userId);
      if (user == null) {
        throw Exception('User not found');
      }

      final currentPreferences = user.notificationPreferences;
      if (currentPreferences == null) {
        LoggingService.instance.logInfo(
          'FirestoreService',
          'No notification preferences found for user: $userId',
        );
        return;
      }

      // Remove device token
      final updatedPreferences = currentPreferences.removeDeviceToken(deviceId);

      await _firestore.collection('users').doc(userId).update({
        'notificationPreferences': updatedPreferences.toJson(),
      });

      LoggingService.instance.logInfo(
        'FirestoreService',
        'Successfully removed FCM token for user: $userId, device: $deviceId',
      );
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        'FirestoreService',
        'Failed to remove FCM token for user: $userId, device: $deviceId',
      );
      throw Exception('Failed to remove FCM token: $e');
    }
  }

  /// Initialize default notification preferences for a user
  static Future<void> initializeUserNotificationPreferences({
    required String userId,
  }) async {
    try {
      LoggingService.instance.logInfo(
        'FirestoreService',
        'Initializing notification preferences for user: $userId',
      );

      final defaultPreferences = NotificationPreferences.defaultPreferences();

      await _firestore.collection('users').doc(userId).update({
        'notificationPreferences': defaultPreferences.toJson(),
      });

      LoggingService.instance.logInfo(
        'FirestoreService',
        'Successfully initialized notification preferences for user: $userId',
      );
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        'FirestoreService',
        'Failed to initialize notification preferences for user: $userId',
      );
      throw Exception('Failed to initialize notification preferences: $e');
    }
  }

  /// Get users subscribed to a specific notification topic
  static Future<List<User>> getUsersSubscribedToTopic(String topic) async {
    try {
      LoggingService.instance.logInfo(
        'FirestoreService',
        'Getting users subscribed to topic: $topic',
      );

      final querySnapshot = await _firestore
          .collection('users')
          .where(
            'notificationPreferences.enablePushNotifications',
            isEqualTo: true,
          )
          .where(
            'notificationPreferences.topicPreferences.$topic',
            isEqualTo: true,
          )
          .get();

      final users = querySnapshot.docs
          .map((doc) => User.fromJson(doc.data()))
          .where(
            (user) =>
                user.notificationPreferences?.fcmTokens.isNotEmpty == true,
          )
          .toList();

      LoggingService.instance.logInfo(
        'FirestoreService',
        'Found ${users.length} users subscribed to topic: $topic',
      );

      return users;
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        'FirestoreService',
        'Failed to get users subscribed to topic: $topic',
      );
      throw Exception('Failed to get users subscribed to topic: $e');
    }
  }

  // Get all active SystemPersona entities, sorted by defaultOrder ascending
  static Future<List<SystemPersona>> getActiveSystemPersonas() async {
    try {
      final querySnapshot = await _firestore
          .collection('systemPersonas')
          .where('isActive', isEqualTo: true)
          .get();

      final personas = querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id; // Add the document ID to the data
        return SystemPersona.fromJson(data);
      }).toList();

      personas.sort((a, b) => a.defaultOrder.compareTo(b.defaultOrder));
      return personas;
    } catch (e) {
      throw Exception('Failed to get system personas: $e');
    }
  }

  // Get SystemPersona by ID
  static Future<SystemPersona?> getSystemPersona(String personaId) async {
    try {
      final docSnapshot = await _firestore
          .collection('systemPersonas')
          .doc(personaId)
          .get();

      if (docSnapshot.exists && docSnapshot.data() != null) {
        final data = docSnapshot.data()!;
        data['id'] = docSnapshot.id; // Add the document ID to the data
        return SystemPersona.fromJson(data);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get system persona: $e');
    }
  }

  // Create a new SystemPersona in Firestore
  static Future<String> createSystemPersona(SystemPersona persona) async {
    try {
      final docRef = await _firestore
          .collection('systemPersonas')
          .add(persona.toJson());

      return docRef.id;
    } catch (e) {
      throw Exception('Failed to create system persona: $e');
    }
  }

  // Batch create multiple SystemPersona entities
  static Future<List<String>> createSystemPersonas(
    List<SystemPersona> personas,
  ) async {
    try {
      final batch = _firestore.batch();
      final docRefs = <DocumentReference>[];

      for (final persona in personas) {
        final docRef = _firestore.collection('systemPersonas').doc();
        batch.set(docRef, persona.toJson());
        docRefs.add(docRef);
      }

      await batch.commit();

      return docRefs.map((ref) => ref.id).toList();
    } catch (e) {
      throw Exception('Failed to create system personas: $e');
    }
  }

  // Get count of SystemPersona entities
  static Future<int> getSystemPersonaCount() async {
    try {
      final querySnapshot = await _firestore
          .collection('systemPersonas')
          .count()
          .get();

      return querySnapshot.count ?? 0;
    } catch (e) {
      throw Exception('Failed to get system persona count: $e');
    }
  }

  // ChatSubjects: read-only retrieval and admin seeding helpers
  static Future<List<ChatSubject>> getActiveChatSubjects() async {
    try {
      final querySnapshot = await _firestore
          .collection('chatSubjects')
          .where('isActive', isEqualTo: true)
          .get();

      final subjects = querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return ChatSubject.fromJson(data);
      }).toList();

      subjects.sort((a, b) => a.defaultOrder.compareTo(b.defaultOrder));
      return subjects;
    } catch (e) {
      throw Exception('Failed to get chat subjects: $e');
    }
  }

  static Future<String> createChatSubject(ChatSubject subject) async {
    try {
      final docRef = await _firestore
          .collection('chatSubjects')
          .add(subject.toJson());
      return docRef.id;
    } catch (e) {
      throw Exception('Failed to create chat subject: $e');
    }
  }

  static Future<List<String>> createChatSubjects(
    List<ChatSubject> subjects,
  ) async {
    try {
      final batch = _firestore.batch();
      final docRefs = <DocumentReference>[];

      for (final subject in subjects) {
        final docRef = _firestore.collection('chatSubjects').doc();
        batch.set(docRef, subject.toJson());
        docRefs.add(docRef);
      }

      await batch.commit();
      return docRefs.map((r) => r.id).toList();
    } catch (e) {
      throw Exception('Failed to create chat subjects: $e');
    }
  }

  static Future<int> getChatSubjectCount() async {
    try {
      final querySnapshot = await _firestore
          .collection('chatSubjects')
          .count()
          .get();
      return querySnapshot.count ?? 0;
    } catch (e) {
      throw Exception('Failed to get chat subject count: $e');
    }
  }

  // Update existing SystemPersona entities with new data
  static Future<void> updateSystemPersonas(List<SystemPersona> personas) async {
    try {
      final batch = _firestore.batch();

      // Get all existing personas to match by name
      final existingPersonas = await getActiveSystemPersonas();
      final existingPersonaMap = <String, String>{};
      for (final persona in existingPersonas) {
        if (persona.id != null) {
          existingPersonaMap[persona.name] = persona.id!;
        }
      }

      // Update personas that exist, create new ones that don't
      for (final persona in personas) {
        final existingId = existingPersonaMap[persona.name];
        if (existingId != null) {
          // Update existing persona
          final docRef = _firestore
              .collection('systemPersonas')
              .doc(existingId);
          batch.update(docRef, persona.toJson());
        } else {
          // Create new persona if it doesn't exist
          final docRef = _firestore.collection('systemPersonas').doc();
          batch.set(docRef, persona.toJson());
        }
      }

      await batch.commit();
    } catch (e) {
      throw Exception('Failed to update system personas: $e');
    }
  }

  // Get UserProfile by userId
  static Future<UserProfile?> getUserProfile(String userId) async {
    try {
      final docSnapshot = await _firestore
          .collection('userProfiles')
          .doc(userId)
          .get();

      if (docSnapshot.exists && docSnapshot.data() != null) {
        return UserProfile.fromJson(docSnapshot.data()!);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get user profile: $e');
    }
  }

  // Create or update UserProfile in Firestore
  static Future<void> createOrUpdateUserProfile(UserProfile userProfile) async {
    try {
      // Normalize fact values to strings before saving to ensure consistency
      final normalizedProfile = userProfile.withNormalizedFactValues();
      await _firestore
          .collection('userProfiles')
          .doc(normalizedProfile.userId)
          .set(normalizedProfile.toJson());
    } catch (e) {
      throw Exception('Failed to create/update user profile: $e');
    }
  }

  // ===== GUIDED PATHS METHODS =====

  // Get all active guided paths
  static Future<List<GuidedPath>> getActiveGuidedPaths() async {
    try {
      final querySnapshot = await _firestore
          .collection('guidedPaths')
          .where('isActive', isEqualTo: true)
          .orderBy('metadata.category_order')
          .orderBy('name')
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id; // Add the document ID to the data
        return GuidedPath.fromJson(data);
      }).toList();
    } catch (e) {
      throw Exception('Failed to get guided paths: $e');
    }
  }

  // Get guided paths accessible to a user based on their tier
  static Future<List<GuidedPath>> getAccessibleGuidedPaths(
    String userId,
  ) async {
    try {
      final user = await getUser(userId);

      // Determine user tier based on subscription status and admin flag
      String userTier = 'free'; // Default to free

      // Check if user is admin (admins get paid access)
      if (user?.isAdmin == true) {
        userTier = 'paid';
      } else {
        // Check subscription status through RevenueCat
        try {
          if (SubscriptionService.instance.isInitialized) {
            final subscription =
                SubscriptionService.instance.currentSubscription;
            if (subscription.hasPremiumAccess) {
              userTier = 'paid';
            }
          }
        } catch (e) {
          // If subscription service fails, log but continue with free tier
          LoggingService.instance.logError(
            e,
            StackTrace.current,
            'FirestoreService',
            'Failed to check subscription status for user $userId',
          );
        }
      }

      final querySnapshot = await _firestore
          .collection('guidedPaths')
          .where('isActive', isEqualTo: true)
          .where('targetUserTier', whereIn: ['free', userTier])
          .orderBy('category')
          .orderBy('name')
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id; // Add the document ID to the data
        return GuidedPath.fromJson(data);
      }).toList();
    } catch (e) {
      throw Exception('Failed to get accessible guided paths: $e');
    }
  }

  // Get guided paths by category
  static Future<List<GuidedPath>> getGuidedPathsByCategory(
    String category,
  ) async {
    try {
      final querySnapshot = await _firestore
          .collection('guidedPaths')
          .where('isActive', isEqualTo: true)
          .where('category', isEqualTo: category)
          .orderBy('name')
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id; // Add the document ID to the data
        return GuidedPath.fromJson(data);
      }).toList();
    } catch (e) {
      throw Exception('Failed to get guided paths by category: $e');
    }
  }

  // Get specific guided path by ID
  static Future<GuidedPath?> getGuidedPath(String pathId) async {
    try {
      final docSnapshot = await _firestore
          .collection('guidedPaths')
          .doc(pathId)
          .get();

      if (docSnapshot.exists && docSnapshot.data() != null) {
        final data = docSnapshot.data()!;
        data['id'] = docSnapshot.id; // Add the document ID to the data
        return GuidedPath.fromJson(data);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get guided path: $e');
    }
  }

  // Create a new guided path
  static Future<String> createGuidedPath(GuidedPath guidedPath) async {
    try {
      final docRef = await _firestore
          .collection('guidedPaths')
          .add(guidedPath.toJson());

      return docRef.id;
    } catch (e) {
      throw Exception('Failed to create guided path: $e');
    }
  }

  // Batch create multiple guided paths
  static Future<List<String>> createGuidedPaths(
    List<GuidedPath> guidedPaths,
  ) async {
    try {
      final batch = _firestore.batch();
      final docRefs = <DocumentReference>[];

      for (final guidedPath in guidedPaths) {
        final docRef = _firestore.collection('guidedPaths').doc();
        batch.set(docRef, guidedPath.toJson());
        docRefs.add(docRef);
      }

      await batch.commit();

      return docRefs.map((ref) => ref.id).toList();
    } catch (e) {
      throw Exception('Failed to create guided paths: $e');
    }
  }

  // ===== PATH STEPS METHODS =====

  // Get all steps for a specific guided path
  static Future<List<PathStep>> getPathSteps(String pathId) async {
    try {
      final querySnapshot = await _firestore
          .collection('pathSteps')
          .where('pathId', isEqualTo: pathId)
          .where('isActive', isEqualTo: true)
          .orderBy('stepNumber')
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id; // Add the document ID to the data
        return PathStep.fromJson(data);
      }).toList();
    } catch (e) {
      debugPrint('Error getting path steps: $e');
      throw Exception('Failed to get path steps: $e');
    }
  }

  // Get specific path step by ID
  static Future<PathStep?> getPathStep(String stepId) async {
    try {
      final docSnapshot = await _firestore
          .collection('pathSteps')
          .doc(stepId)
          .get();

      if (docSnapshot.exists && docSnapshot.data() != null) {
        final data = docSnapshot.data()!;
        data['id'] = docSnapshot.id; // Add the document ID to the data
        return PathStep.fromJson(data);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting path steps: $e');
      throw Exception('Failed to get path step: $e');
    }
  }

  // Create a new path step
  static Future<String> createPathStep(PathStep pathStep) async {
    try {
      final docRef = await _firestore
          .collection('pathSteps')
          .add(pathStep.toJson());

      return docRef.id;
    } catch (e) {
      throw Exception('Failed to create path step: $e');
    }
  }

  // Batch create multiple path steps
  static Future<List<String>> createPathSteps(List<PathStep> pathSteps) async {
    try {
      final batch = _firestore.batch();
      final docRefs = <DocumentReference>[];

      for (final pathStep in pathSteps) {
        final docRef = _firestore.collection('pathSteps').doc();
        batch.set(docRef, pathStep.toJson());
        docRefs.add(docRef);
      }

      await batch.commit();

      return docRefs.map((ref) => ref.id).toList();
    } catch (e) {
      throw Exception('Failed to create path steps: $e');
    }
  }

  // ===== USER PATH PROGRESS METHODS =====

  // Get user's progress for a specific path
  static Future<UserPathProgress?> getUserPathProgress(
    String userId,
    String pathId,
  ) async {
    try {
      final querySnapshot = await _firestore
          .collection('userPathProgress')
          .where('userId', isEqualTo: userId)
          .where('pathId', isEqualTo: pathId)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        final data = querySnapshot.docs.first.data();
        data['id'] =
            querySnapshot.docs.first.id; // Add the document ID to the data
        return UserPathProgress.fromJson(data);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get user path progress: $e');
    }
  }

  // Get all user's path progress
  static Future<List<UserPathProgress>> getUserAllPathProgress(
    String userId,
  ) async {
    try {
      final querySnapshot = await _firestore
          .collection('userPathProgress')
          .where('userId', isEqualTo: userId)
          .orderBy('lastAccessedDate', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id; // Add the document ID to the data
        return UserPathProgress.fromJson(data);
      }).toList();
    } catch (e) {
      throw Exception('Failed to get user path progress: $e');
    }
  }

  // Create or update user path progress
  static Future<String> createOrUpdateUserPathProgress(
    UserPathProgress progress,
  ) async {
    try {
      // Check if progress already exists
      final existingProgress = await getUserPathProgress(
        progress.userId,
        progress.pathId,
      );

      if (existingProgress != null) {
        // Update existing progress
        await _firestore
            .collection('userPathProgress')
            .doc(existingProgress.id!)
            .update(progress.toJson());
        return existingProgress.id!;
      } else {
        // Create new progress
        final docRef = await _firestore
            .collection('userPathProgress')
            .add(progress.toJson());
        return docRef.id;
      }
    } catch (e) {
      throw Exception('Failed to create/update user path progress: $e');
    }
  }

  // Start a path for a user
  static Future<String> startUserPath(String userId, String pathId) async {
    try {
      final now = DateTime.now();
      final progress = UserPathProgress(
        userId: userId,
        pathId: pathId,
        isCompleted: false,
        startedDate: now,
        lastAccessedDate: now,
      );

      return await createOrUpdateUserPathProgress(progress);
    } catch (e) {
      throw Exception('Failed to start user path: $e');
    }
  }

  // Complete a step for a user (legacy method - now uses stepProgress system)
  static Future<void> completeUserPathStep(
    String userId,
    String pathId,
    String stepId,
  ) async {
    try {
      final existingProgress = await getUserPathProgress(userId, pathId);
      if (existingProgress == null) {
        throw Exception('User path progress not found');
      }

      // Use the new stepProgress system
      final updatedProgress = existingProgress.markStepCompleted(stepId);

      await createOrUpdateUserPathProgress(updatedProgress);
    } catch (e) {
      throw Exception('Failed to complete user path step: $e');
    }
  }

  // Get count of guided paths
  static Future<int> getGuidedPathCount() async {
    try {
      final querySnapshot = await _firestore
          .collection('guidedPaths')
          .count()
          .get();

      return querySnapshot.count ?? 0;
    } catch (e) {
      throw Exception('Failed to get guided path count: $e');
    }
  }
}
