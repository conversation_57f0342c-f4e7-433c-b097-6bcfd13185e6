import 'package:flutter/material.dart';
import '../../theme/theme.dart';
import '../onboarding/coach_chip.dart';

/// A reusable widget for selecting goals for life areas
///
/// This widget provides:
/// - Multiple goal selection with chips
/// - "I'm satisfied" option that clears other selections
/// - Consistent styling with onboarding flow
/// - Callback for goal changes
class LifeAreaGoalsWidget extends StatefulWidget {
  final String areaName;
  final List<String> goalOptions;
  final List<String>? initialGoals;
  final void Function(List<String> goals) onGoalsChanged;
  final bool showAreaName;

  const LifeAreaGoalsWidget({
    super.key,
    required this.areaName,
    required this.goalOptions,
    this.initialGoals,
    required this.onGoalsChanged,
    this.showAreaName = true,
  });

  @override
  State<LifeAreaGoalsWidget> createState() => _LifeAreaGoalsWidgetState();
}

class _LifeAreaGoalsWidgetState extends State<LifeAreaGoalsWidget> {
  static const String satisfiedOption = "I'm satisfied with this area";

  late List<String> _selected;

  @override
  void initState() {
    super.initState();
    _selected = List.from(widget.initialGoals ?? <String>[]);
  }

  @override
  void didUpdateWidget(LifeAreaGoalsWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initialGoals != oldWidget.initialGoals) {
      _selected = List.from(widget.initialGoals ?? <String>[]);
    }
  }

  void _toggle(String goal) {
    setState(() {
      if (goal == satisfiedOption) {
        if (_selected.contains(satisfiedOption)) {
          _selected.remove(satisfiedOption);
        } else {
          _selected
            ..clear()
            ..add(satisfiedOption);
        }
      } else {
        _selected.remove(satisfiedOption);
        if (_selected.contains(goal)) {
          _selected.remove(goal);
        } else {
          _selected.add(goal);
        }
      }
    });
    widget.onGoalsChanged(List.from(_selected));
  }

  @override
  Widget build(BuildContext context) {
    final allOptions = [...widget.goalOptions, satisfiedOption];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showAreaName) ...[
          // Area name
          Text(
            widget.areaName,
            style: context.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),

          const SizedBox(height: AppDimensions.spacingS),
        ],

        // Subtitle
        Text(
          'Choose goals to improve this area',
          style: context.textTheme.bodyLarge?.copyWith(
            color: context.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),

        const SizedBox(height: AppDimensions.spacingL),

        // Goal options
        ...allOptions.map((goal) {
          final isSelected = _selected.contains(goal);
          final isSatisfiedOption = goal == satisfiedOption;

          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: CoachChip(
              text: goal,
              isSelected: isSelected,
              fullWidth: true,
              onTap: () => _toggle(goal),
              variant: isSatisfiedOption
                  ? CoachChipVariant.selection
                  : CoachChipVariant.selection,
            ),
          );
        }),
      ],
    );
  }
}
