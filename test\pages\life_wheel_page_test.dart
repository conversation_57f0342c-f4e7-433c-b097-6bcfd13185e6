import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/pages/life_wheel/life_wheel_page.dart';
import 'package:upshift/theme/theme.dart';

void main() {
  group('LifeWheelPage', () {
    testWidgets('should display app bar with correct title', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const LifeWheelPage(),
        ),
      );

      // Wait for the widget to settle
      await tester.pumpAndSettle();

      // Check if app bar is present with correct title
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.text('Life Wheel'), findsOneWidget);
    });

    testWidgets('should show loading indicator initially', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const LifeWheelPage(),
        ),
      );

      // Should show loading indicator initially
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should be a Scaffold widget', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const LifeWheelPage(),
        ),
      );

      expect(find.byType(Scaffold), findsOneWidget);
    });
  });
}
