import 'package:flutter/material.dart';

/// Comprehensive color palette for the Upshift app
/// Following the "Growth Through Clarity" design philosophy
class AppColors {
  // Figma Design System Colors - Exact Match
  static const Color bg0 = Color(0xFF0B0C0E); // Main background
  static const Color bg1 = Color(0xFF131417); // Secondary surface
  static const Color bg2 = Color(0xFF1C1E22); // Tertiary surface
  static const Color line = Color(0xFF2B2E34); // Borders/dividers
  static const Color textPrimary = Color(0xFFF5F7FA); // Primary text
  static const Color textSecondary = Color(0xFFA9AFB9); // Secondary text
  static const Color accent = Color(0xFFF8D94E); // Yellow brand accent
  static const Color accentPressed = Color(0xFFE8C83A); // Pressed state
  static const Color chipSelection = Color(0xFFEBFF57); // Selection chips
  static const Color chipSelectionPressed = Color(
    0xFFD9F542,
  ); // Pressed selection

  // Primary Colors - Using Figma Design System
  static const Color primary = accent; // Use accent as primary
  static const Color primaryVariant =
      accentPressed; // Use pressed state as variant
  static const Color primaryLight = Color(0xFFFFE55C); // Light Yellow
  static const Color primaryDark = accentPressed; // Use pressed state as dark
  static const Color primaryContainer = Color(
    0xFFFFF8E1,
  ); // Light Yellow Container
  static const Color primaryContainerDark = Color(
    0xFF2A2A2A,
  ); // Dark Container (matching screenshot aesthetic)
  static const Color onPrimary = Color(
    0xFF000000,
  ); // Black on primary (optimal contrast)

  // Better primary colors for light theme readability
  static const Color primaryTextLight = Color(
    0xFFB8860B,
  ); // Darker yellow for text
  static const Color primaryAccentLight = Color(
    0xFFE8C83A,
  ); // Medium yellow for accents

  // Secondary Colors - Dark Theme (Extracted from screenshots)
  static const Color secondary = Color(0xFF2A2A2A); // Dark Grey
  static const Color secondaryVariant = Color(0xFF1A1A1A); // Very Dark Grey
  static const Color secondaryContainer = Color(
    0xFFF5F5F5,
  ); // Light Grey Container
  static const Color secondaryContainerDark = Color(
    0xFF1A1A1A,
  ); // Very Dark Container (matching screenshot)
  static const Color onSecondary = Color(0xFFFFFFFF); // White on secondary

  // Additional Colors
  static const Color success = Color(0xFF4CAF50); // Success Green
  static const Color warning = Color(0xFFFF9800); // Warm Orange
  static const Color error = Color(0xFFF44336); // Coral Red
  static const Color info = Color(0xFF2196F3); // Sky Blue
  static const Color onError = Color(0xFFFFFFFF); // White on error

  // Surface Colors - Light Theme (Updated for better contrast)
  static const Color surfaceLight = Color(0xFFFAFAFA); // Light Surface
  static const Color backgroundLight = Color(0xFFFFFFFF); // White Background
  static const Color onSurfaceLight = Color(
    0xFF212121,
  ); // Dark text on light surface
  static const Color onBackgroundLight = Color(
    0xFF424242,
  ); // Dark text on light background

  // Light theme card and component colors for better visibility
  static const Color cardLight = Color(0xFFFFFFFF); // White card background
  static const Color cardBorderLight = Color(0xFFE0E0E0); // Light grey border
  static const Color surfaceElevatedLight = Color(
    0xFFF5F5F5,
  ); // Slightly elevated surface

  // Surface Colors - Dark Theme (Updated to match screenshot aesthetic)
  static const Color surfaceDark = Color(
    0xFF1A1A1A,
  ); // Very Dark Surface (matching screenshots)
  static const Color backgroundDark = Color(0xFF121212); // Pure Dark Background
  static const Color onSurfaceDark = Color(
    0xFFE0E0E0,
  ); // Light text on dark surface
  static const Color onBackgroundDark = Color(
    0xFFFFFFFF,
  ); // White text on dark background

  // Card and Container Colors for Dark Theme (Updated for two-color system)
  static const Color cardDark = Color(0xFF2A2A2A); // Dark card background
  static const Color cardBorderDark = Color(
    0xFF404040,
  ); // Subtle grey border for cards

  // Category-Specific Colors (Enhanced from existing implementation)
  static const Color focusProductivity = Color(0xFF1976D2); // Professional Blue
  static const Color mindsetResilience = Color(0xFF388E3C); // Growth Green
  static const Color habitFormation = Color(0xFFF57C00); // Energy Orange
  static const Color lifeDesign = Color(0xFF7B1FA2); // Vision Purple

  // Neutral Grays
  static const Color gray50 = Color(0xFFFAFAFA);
  static const Color gray100 = Color(0xFFF5F5F5);
  static const Color gray200 = Color(0xFFEEEEEE);
  static const Color gray300 = Color(0xFFE0E0E0);
  static const Color gray400 = Color(0xFFBDBDBD);
  static const Color gray500 = Color(0xFF9E9E9E);
  static const Color gray600 = Color(0xFF757575);
  static const Color gray700 = Color(0xFF616161);
  static const Color gray800 = Color(0xFF424242);
  static const Color gray900 = Color(0xFF212121);

  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primary, primaryVariant],
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [secondary, secondaryVariant],
  );

  static const LinearGradient successGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [success, Color(0xFF2E7D32)],
  );

  // Chat-specific colors (Updated for two-color system)
  static const Color userMessageBackground = primary;
  static const Color aiMessageBackground = Color(0xFFF5F5F5);
  static const Color aiMessageBackgroundDark = Color(0xFF2A2A2A);
  static const Color chatInputBackground = Color(0xFFF8F9FA);
  static const Color chatInputBackgroundDark = Color(0xFF1A1A1A);

  // Progress and Achievement Colors
  static const Color progressTrack = Color(0xFFE0E0E0);
  static const Color progressTrackDark = Color(0xFF424242);
  static const Color achievementGold = Color(0xFFFFD700);
  static const Color achievementSilver = Color(0xFFC0C0C0);
  static const Color achievementBronze = Color(0xFFCD7F32);

  // Button State Colors (for the two-color system)
  static const Color primaryHover = Color(
    0xFFFFE55C,
  ); // Lighter yellow on hover
  static const Color primaryPressed = Color(
    0xFFB8860B,
  ); // Darker yellow when pressed
  static const Color primaryDisabled = Color(0xFF666666); // Grey when disabled
  static const Color primaryDisabledText = Color(
    0xFF999999,
  ); // Grey text when disabled

  // Dark theme button states
  static const Color secondaryHover = Color(
    0xFF404040,
  ); // Lighter dark on hover
  static const Color secondaryPressed = Color(0xFF0A0A0A); // Darker on pressed
  static const Color secondaryDisabled = Color(0xFF1A1A1A); // Disabled dark

  // Surface elevation colors for dark theme
  static const Color surfaceElevation1 = Color(0xFF1E1E1E); // Slight elevation
  static const Color surfaceElevation2 = Color(0xFF232323); // Medium elevation
  static const Color surfaceElevation3 = Color(0xFF282828); // High elevation

  /// Get category color by name
  static Color getCategoryColor(String category) {
    switch (category) {
      case 'Focus & Productivity':
        return focusProductivity;
      case 'Mindset & Resilience':
        return mindsetResilience;
      case 'Habit Formation':
        return habitFormation;
      case 'Life Design':
        return lifeDesign;
      default:
        return primary;
    }
  }

  /// Get category color with opacity
  static Color getCategoryColorWithOpacity(String category, double opacity) {
    return getCategoryColor(category).withValues(alpha: opacity);
  }

  /// Get difficulty color
  static Color getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return success;
      case 'intermediate':
        return warning;
      case 'advanced':
        return error;
      default:
        return gray500;
    }
  }

  /// Get tier-based colors
  static Color getTierColor(String tier) {
    switch (tier.toLowerCase()) {
      case 'free':
        return success;
      case 'paid':
      case 'premium':
        return achievementGold;
      default:
        return gray500;
    }
  }

  /// Get status colors
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'success':
        return success;
      case 'in_progress':
      case 'current':
        return primary;
      case 'locked':
      case 'disabled':
        return gray400;
      case 'error':
      case 'failed':
        return error;
      case 'warning':
        return warning;
      default:
        return gray500;
    }
  }

  /// Create a color with adaptive opacity based on theme brightness
  static Color adaptiveColor(
    Color color,
    Brightness brightness, {
    double lightOpacity = 1.0,
    double darkOpacity = 0.8,
  }) {
    return brightness == Brightness.light
        ? color.withValues(alpha: lightOpacity)
        : color.withValues(alpha: darkOpacity);
  }

  /// Get surface color with elevation tint
  static Color getSurfaceWithElevation(
    ColorScheme colorScheme,
    double elevation,
  ) {
    if (colorScheme.brightness == Brightness.dark) {
      // Apply elevation tint for dark theme
      final tintOpacity = (elevation / 24).clamp(0.0, 0.15);
      return Color.alphaBlend(
        colorScheme.primary.withValues(alpha: tintOpacity),
        colorScheme.surface,
      );
    }
    return colorScheme.surface;
  }
}
