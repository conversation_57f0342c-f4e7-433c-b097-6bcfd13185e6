import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/services/life_wheel_service.dart';

void main() {
  group('LifeWheelService', () {
    test('should have correct number of life areas', () {
      expect(LifeWheelService.lifeAreas.length, equals(8));
    });

    test('should have all required life area keys', () {
      final expectedKeys = [
        'personal_growth',
        'finances',
        'career',
        'health',
        'relationships',
        'social',
        'recreation',
        'environment',
      ];

      final actualKeys = LifeWheelService.lifeAreas
          .map((area) => area['key'] as String)
          .toList();

      expect(actualKeys, equals(expectedKeys));
    });

    test('should parse life wheel data correctly', () {
      final testData = {
        'areas': [
          {
            'key': 'personal_growth',
            'name': 'Personal Growth & Learning',
            'rating': 8,
            'goals': ['Read weekly', 'Take course'],
          },
          {
            'key': 'finances',
            'name': 'Finances & Money Management',
            'rating': 6,
            'goals': ['Create budget'],
          },
        ],
        'lastUpdated': '2024-01-01T00:00:00.000Z',
        'source': 'test',
      };

      final result = LifeWheelService.parseLifeWheelData(testData);
      final ratings = result['ratings'] as Map<String, int>;
      final goals = result['goals'] as Map<String, List<String>>;

      expect(ratings['personal_growth'], equals(8));
      expect(ratings['finances'], equals(6));
      expect(goals['personal_growth'], equals(['Read weekly', 'Take course']));
      expect(goals['finances'], equals(['Create budget']));
    });

    test('should handle null life wheel data', () {
      final result = LifeWheelService.parseLifeWheelData(null);
      final ratings = result['ratings'] as Map<String, int>;
      final goals = result['goals'] as Map<String, List<String>>;

      expect(ratings.isEmpty, isTrue);
      expect(goals.isEmpty, isTrue);
    });

    test('should handle empty life wheel data', () {
      final result = LifeWheelService.parseLifeWheelData({});
      final ratings = result['ratings'] as Map<String, int>;
      final goals = result['goals'] as Map<String, List<String>>;

      expect(ratings.isEmpty, isTrue);
      expect(goals.isEmpty, isTrue);
    });

    test('each life area should have required fields', () {
      for (final area in LifeWheelService.lifeAreas) {
        expect(area.containsKey('key'), isTrue);
        expect(area.containsKey('name'), isTrue);
        expect(area.containsKey('desc'), isTrue);
        expect(area.containsKey('goals'), isTrue);
        
        expect(area['key'], isA<String>());
        expect(area['name'], isA<String>());
        expect(area['desc'], isA<String>());
        expect(area['goals'], isA<List>());
        
        final goals = area['goals'] as List;
        expect(goals.length, greaterThan(0));
        expect(goals.every((goal) => goal is String), isTrue);
      }
    });
  });
}
