import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../models/models.dart';
import '../../services/firestore.dart';
import '../../services/life_wheel_service.dart';
import '../../services/analytics_service.dart';
import '../../services/performance_service.dart';
import '../../services/logging_service.dart';
import '../../theme/theme.dart';
import '../../widgets/persona_video_modal.dart';
import '../chat_page.dart';
import 'name_input_step.dart';
import 'age_range_step.dart';
import 'coach_selection_step.dart';
import 'life_wheel_intro_step.dart';
import 'life_area_rating_step.dart';
import 'life_area_goals_step.dart';

/// Main onboarding flow controller that manages the 5-step process
class OnboardingFlow extends StatefulWidget {
  const OnboardingFlow({super.key});

  @override
  State<OnboardingFlow> createState() => _OnboardingFlowState();
}

class _OnboardingFlowState extends State<OnboardingFlow> {
  int _currentStep = 0;
  bool _isLoading = false;

  // Onboarding data
  String _userName = '';
  String? _ageRange;
  SystemPersona? _selectedCoach;

  // Life Wheel data
  final Map<String, int> _areaRatings = {};
  final Map<String, List<String>> _areaGoals = {};
  static const int _totalSteps = 20; // 1..20 as specified

  // System personas for coach selection
  List<SystemPersona> _systemPersonas = [];
  bool _isLoadingPersonas = false;

  @override
  void initState() {
    super.initState();
    _loadSystemPersonas();
  }

  Future<void> _loadSystemPersonas() async {
    setState(() {
      _isLoadingPersonas = true;
    });

    try {
      final personas = await FirestoreService.getActiveSystemPersonas();
      setState(() {
        _systemPersonas = personas;
        _isLoadingPersonas = false;
      });
    } catch (e) {
      LoggingService.instance.logError(
        e,
        StackTrace.current,
        'OnboardingFlow',
        'Failed to load system personas',
      );
      setState(() {
        _isLoadingPersonas = false;
      });
    }
  }

  void _nextStep() {
    if (_currentStep < 19) {
      setState(() {
        _currentStep++;
      });
    } else {
      _finalizeAndStartChat();
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
    }
  }

  void _skipStep() {
    _nextStep();
  }

  Future<void> _finalizeAndStartChat() async {
    setState(() => _isLoading = true);

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Persist onboarding basics
      await PerformanceService.instance.measureOnboardingCompletion(() async {
        await FirestoreService.updateUserOnboarding(
          userId: currentUser.uid,
          name: _userName,
          description: null,
          preferredPersonaIds:
              _selectedCoach != null && _selectedCoach!.id != null
              ? [_selectedCoach!.id!]
              : [],
        );

        // Load or create UserProfile
        var profile = await FirestoreService.getUserProfile(currentUser.uid);
        profile ??= UserProfile(
          userId: currentUser.uid,
          name: _userName,
          age: null,
          gender: null,
          familyStatus: null,
          family: null,
          location: null,
          facts: null,
          likes: null,
          dislikes: null,
          preferences: {},
          goals: null,
          personalityTraits: null,
          interactionHistory: InteractionHistory(
            lastUpdated: DateTime.now(),
            sources: const [],
          ),
        );

        // Merge preferences with life wheel data
        final prefs = Map<String, dynamic>.from(profile.preferences ?? {});
        final areas = LifeWheelService.lifeAreas
            .map(
              (a) => {
                'key': a['key'],
                'name': a['name'],
                'rating': _areaRatings[a['key']] ?? 0,
                'goals': _areaGoals[a['key']] ?? <String>[],
              },
            )
            .toList();

        prefs['ageRange'] = _ageRange;
        prefs['lifeWheel'] = {
          'areas': areas,
          'lastUpdated': DateTime.now().toIso8601String(),
          'source': 'onboarding_life_wheel_v1',
        };

        final updatedProfile = profile.copyWith(
          name: _userName.isNotEmpty ? _userName : profile.name,
          preferences: prefs,
          interactionHistory: profile.interactionHistory.copyWith(
            lastUpdated: DateTime.now(),
          ),
        );

        await FirestoreService.createOrUpdateUserProfile(updatedProfile);

        // Analytics
        await AnalyticsService.instance.logOnboardingComplete();
        await AnalyticsService.instance.setUserProperty(
          name: 'is_onboarded',
          value: 'true',
        );
        if (_selectedCoach != null) {
          await AnalyticsService.instance.logPersonaSelected(
            personaId: _selectedCoach!.id ?? '',
            selectionContext: 'onboarding',
          );
        }
      }, _areaGoals.length);

      // Create first chat with the selected persona
      if (_selectedCoach?.id == null) {
        throw Exception('Please select a coach to continue');
      }

      final chatId = await FirestoreService.createChat(
        currentUser.uid,
        systemPersonaId: _selectedCoach!.id!,
      );
      final chat = await FirestoreService.getChat(currentUser.uid, chatId);

      if (mounted && chat != null) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => ChatPage(chat: chat)),
        );
      }
    } catch (e) {
      await LoggingService.instance.logError(
        e,
        StackTrace.current,
        'OnboardingFlow',
        'Failed to finalize onboarding',
      );
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to finish onboarding: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        backgroundColor: AppColors.bg0,
        body: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.accent),
          ),
        ),
      );
    }

    switch (_currentStep) {
      case 0:
        return NameInputStep(
          onNameChanged: (name) => _userName = name,
          onBack: null,
          onSkip: _skipStep,
          onContinue: _nextStep,
          initialName: _userName,
          progressCurrent: 1,
          progressTotal: _totalSteps,
        );

      case 1:
        return AgeRangeStep(
          onAgeRangeChanged: (ageRange) => _ageRange = ageRange,
          onBack: _previousStep,
          onSkip: _skipStep,
          onContinue: _nextStep,
          initialAgeRange: _ageRange,
          progressCurrent: 2,
          progressTotal: _totalSteps,
        );

      case 2:
        return LifeWheelIntroStep(
          onBack: _previousStep,
          onContinue: _nextStep,
          progressCurrent: 3,
          progressTotal: _totalSteps,
        );

      // Cases 3..18: 8 areas * (rating + goals)
      default:
        if (_currentStep >= 3 && _currentStep <= 18) {
          final pairIndex = _currentStep - 3; // 0..15
          final areaIndex = pairIndex ~/ 2; // 0..7
          final isRating = pairIndex % 2 == 0; // even = rating
          final area = LifeWheelService.lifeAreas[areaIndex];
          final key = area['key'] as String;
          final name = area['name'] as String;
          final desc = area['desc'] as String;
          final goals = (area['goals'] as List).cast<String>();
          final progress = _currentStep + 1; // 1-based

          if (isRating) {
            return LifeAreaRatingStep(
              areaName: name,
              areaDescription: desc,
              initialRating: _areaRatings[key],
              onRatingChanged: (r) => _areaRatings[key] = r,
              onBack: _previousStep,
              onSkip: _skipStep,
              onContinue: _nextStep,
              progressCurrent: progress,
              progressTotal: _totalSteps,
            );
          }

          return LifeAreaGoalsStep(
            areaName: name,
            goalOptions: goals,
            initialGoals: _areaGoals[key],
            onGoalsChanged: (list) => _areaGoals[key] = list,
            onBack: _previousStep,
            onSkip: _skipStep,
            onContinue: _nextStep,
            progressCurrent: progress,
            progressTotal: _totalSteps,
          );
        }

        // Case 19: Persona selection
        if (_currentStep == 19) {
          return CoachSelectionStep(
            coaches: _systemPersonas,
            onCoachSelected: (coach) => _selectedCoach = coach,
            onVideoTap: (coach) {
              LoggingService.instance.logInfo(
                'OnboardingFlow',
                'Video tap for coach: ${coach.name}',
              );
              final currentIndex = _systemPersonas.indexWhere(
                (p) => p.id == coach.id,
              );
              PersonaVideoModal.show(
                context,
                personas: _systemPersonas,
                initialIndex: currentIndex >= 0 ? currentIndex : 0,
                onPersonaChanged: (changedPersona) {},
              );
            },
            onBack: _previousStep,
            onContinue: _nextStep,
            initialSelectedCoach: _selectedCoach,
            userName: _userName,
            progressCurrent: 20,
            progressTotal: _totalSteps,
          );
        }

        // Fallback (should not happen)
        return NameInputStep(
          onNameChanged: (name) => _userName = name,
          onBack: null,
          onSkip: _skipStep,
          onContinue: _nextStep,
          initialName: _userName,
          progressCurrent: 1,
          progressTotal: _totalSteps,
        );
    }
  }
}
