import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'app_colors.dart';
import 'app_typography.dart';

class AppTheme {
  static ThemeData get lightTheme {
    final colorScheme = ColorScheme.fromSeed(
      seedColor:
          AppColors.primaryTextLight, // Use darker yellow for better contrast
      brightness: Brightness.light,
      primary: AppColors.primaryTextLight, // Darker yellow for text/UI elements
      primaryContainer: AppColors.primaryContainer,
      secondary: AppColors.secondary,
      secondaryContainer: AppColors.secondaryContainer,
      surface: AppColors.surfaceLight,
      surfaceContainerHighest:
          AppColors.surfaceElevatedLight, // For elevated components
      error: AppColors.error,
      onPrimary: AppColors.onPrimary,
      onSecondary: AppColors.onSecondary,
      onSurface: AppColors.onSurfaceLight,
      onError: AppColors.onError,
      outline: AppColors.cardBorderLight, // For borders and dividers
    );

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      textTheme: AppTypography.textTheme,
      appBarTheme: _appBarTheme(colorScheme),
      elevatedButtonTheme: _elevatedButtonTheme(colorScheme),
      outlinedButtonTheme: _outlinedButtonTheme(colorScheme),
      textButtonTheme: _textButtonTheme(colorScheme),
      floatingActionButtonTheme: _fabTheme(colorScheme),
      cardTheme: _cardTheme(colorScheme),
      bottomNavigationBarTheme: _bottomNavTheme(colorScheme),
      inputDecorationTheme: _inputDecorationTheme(colorScheme),
      progressIndicatorTheme: _progressIndicatorTheme(colorScheme),
      chipTheme: _chipTheme(colorScheme),
      dividerTheme: _dividerTheme(colorScheme),
      scaffoldBackgroundColor: colorScheme.surface,
    );
  }

  static ThemeData get darkTheme {
    final colorScheme = ColorScheme.fromSeed(
      seedColor: AppColors.primary,
      brightness: Brightness.dark,
      primary: AppColors.primary, // Bright yellow
      primaryContainer: AppColors.primaryContainerDark, // Dark container
      secondary: AppColors.secondary, // Dark grey
      secondaryContainer: AppColors.secondaryContainerDark, // Very dark
      surface: AppColors.surfaceDark, // Very dark surface
      error: AppColors.error,
      onPrimary: AppColors.onPrimary, // Black text on yellow
      onSecondary: AppColors.onSecondary, // White text on dark
      onSurface: AppColors.onSurfaceDark, // Light text on dark surface
      onError: AppColors.onError,
    );

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      textTheme: AppTypography.textTheme,
      appBarTheme: _appBarTheme(colorScheme),
      elevatedButtonTheme: _elevatedButtonTheme(colorScheme),
      outlinedButtonTheme: _outlinedButtonTheme(colorScheme),
      textButtonTheme: _textButtonTheme(colorScheme),
      floatingActionButtonTheme: _fabTheme(colorScheme),
      cardTheme: _cardTheme(colorScheme),
      bottomNavigationBarTheme: _bottomNavTheme(colorScheme),
      inputDecorationTheme: _inputDecorationTheme(colorScheme),
      progressIndicatorTheme: _progressIndicatorTheme(colorScheme),
      chipTheme: _chipTheme(colorScheme),
      dividerTheme: _dividerTheme(colorScheme),
      scaffoldBackgroundColor: colorScheme.brightness == Brightness.dark
          ? AppColors.backgroundDark
          : colorScheme.surface,
    );
  }

  static AppBarTheme _appBarTheme(ColorScheme colorScheme) {
    final isDark = colorScheme.brightness == Brightness.dark;

    return AppBarTheme(
      elevation: isDark ? 0 : 1, // Slight elevation for light theme
      scrolledUnderElevation: 4,
      backgroundColor: isDark
          ? colorScheme.surface
          : AppColors
                .surfaceElevatedLight, // Slightly different background for light theme
      foregroundColor: colorScheme.onSurface,
      titleTextStyle: AppTypography.textTheme.titleLarge?.copyWith(
        color: colorScheme.onSurface,
        fontWeight: FontWeight.w600,
      ),
      systemOverlayStyle: colorScheme.brightness == Brightness.light
          ? SystemUiOverlayStyle.dark
          : SystemUiOverlayStyle.light,
      shadowColor: isDark
          ? Colors.transparent
          : AppColors.gray300.withValues(
              alpha: 0.2,
            ), // Subtle shadow for light theme
    );
  }

  static ElevatedButtonThemeData _elevatedButtonTheme(ColorScheme colorScheme) {
    return ElevatedButtonThemeData(
      style: ButtonStyle(
        elevation: WidgetStateProperty.all(
          0,
        ), // Flat design like in screenshots
        padding: WidgetStateProperty.all(
          const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
        ),
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              25,
            ), // More rounded like screenshots
          ),
        ),
        textStyle: WidgetStateProperty.all(
          AppTypography.textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        // State-based background colors
        backgroundColor: WidgetStateProperty.resolveWith<Color?>((
          Set<WidgetState> states,
        ) {
          if (states.contains(WidgetState.disabled)) {
            return AppColors.primaryDisabled;
          }
          if (states.contains(WidgetState.pressed)) {
            return AppColors.primaryPressed;
          }
          if (states.contains(WidgetState.hovered)) {
            return AppColors.primaryHover;
          }
          // Use bright yellow for buttons (more acceptable than for text)
          return AppColors.primary;
        }),
        // State-based foreground colors
        foregroundColor: WidgetStateProperty.resolveWith<Color?>((
          Set<WidgetState> states,
        ) {
          if (states.contains(WidgetState.disabled)) {
            return AppColors.primaryDisabledText;
          }
          return AppColors.onPrimary;
        }),
        // Overlay color for ripple effects
        overlayColor: WidgetStateProperty.resolveWith<Color?>((
          Set<WidgetState> states,
        ) {
          if (states.contains(WidgetState.hovered)) {
            return AppColors.primaryHover.withValues(alpha: 0.1);
          }
          if (states.contains(WidgetState.pressed)) {
            return AppColors.primaryPressed.withValues(alpha: 0.2);
          }
          return null;
        }),
      ),
    );
  }

  static OutlinedButtonThemeData _outlinedButtonTheme(ColorScheme colorScheme) {
    return OutlinedButtonThemeData(
      style: ButtonStyle(
        padding: WidgetStateProperty.all(
          const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
        ),
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25), // Match ElevatedButton
          ),
        ),
        side: WidgetStateProperty.resolveWith<BorderSide?>((
          Set<WidgetState> states,
        ) {
          if (states.contains(WidgetState.disabled)) {
            return BorderSide(color: AppColors.primaryDisabled, width: 2);
          }
          if (states.contains(WidgetState.pressed)) {
            return BorderSide(color: AppColors.primaryPressed, width: 2);
          }
          if (states.contains(WidgetState.hovered)) {
            return BorderSide(color: AppColors.primaryHover, width: 2);
          }
          return BorderSide(color: AppColors.primary, width: 2);
        }),
        foregroundColor: WidgetStateProperty.resolveWith<Color?>((
          Set<WidgetState> states,
        ) {
          if (states.contains(WidgetState.disabled)) {
            return AppColors.primaryDisabledText;
          }
          if (states.contains(WidgetState.pressed)) {
            return AppColors.primaryPressed;
          }
          if (states.contains(WidgetState.hovered)) {
            return AppColors.primaryHover;
          }
          // Use bright yellow for outlined button text/border
          return AppColors.primary;
        }),
        textStyle: WidgetStateProperty.all(
          AppTypography.textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
      ),
    );
  }

  static TextButtonThemeData _textButtonTheme(ColorScheme colorScheme) {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        textStyle: AppTypography.textTheme.labelLarge?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  static FloatingActionButtonThemeData _fabTheme(ColorScheme colorScheme) {
    return FloatingActionButtonThemeData(
      elevation: 6,
      highlightElevation: 12,
      backgroundColor: AppColors.primary, // Use bright yellow for FAB
      foregroundColor: AppColors.onPrimary,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
      extendedPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
    );
  }

  static CardThemeData _cardTheme(ColorScheme colorScheme) {
    final isDark = colorScheme.brightness == Brightness.dark;

    return CardThemeData(
      elevation: isDark ? 0 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(
          20,
        ), // More rounded like screenshots
        side: isDark
            ? BorderSide(
                color: AppColors.cardBorderDark.withValues(alpha: 0.2),
                width: 1,
              )
            : BorderSide(
                color: AppColors.cardBorderLight,
                width: 1,
              ), // Add border for light theme visibility
      ),
      color: isDark ? AppColors.cardDark : AppColors.cardLight,
      shadowColor: isDark
          ? Colors.transparent
          : AppColors.gray300.withValues(
              alpha: 0.3,
            ), // Subtle shadow for light theme
    );
  }

  static BottomNavigationBarThemeData _bottomNavTheme(ColorScheme colorScheme) {
    final isDark = colorScheme.brightness == Brightness.dark;

    return BottomNavigationBarThemeData(
      elevation: 8,
      backgroundColor: isDark
          ? colorScheme.surface
          : AppColors
                .surfaceElevatedLight, // Slightly different background for light theme
      selectedItemColor: colorScheme.primary,
      unselectedItemColor: colorScheme.onSurface.withValues(alpha: 0.6),
      selectedLabelStyle: AppTypography.textTheme.labelMedium?.copyWith(
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: AppTypography.textTheme.labelMedium?.copyWith(
        fontWeight: FontWeight.w400,
      ),
      type: BottomNavigationBarType.fixed,
    );
  }

  static InputDecorationTheme _inputDecorationTheme(ColorScheme colorScheme) {
    final isDark = colorScheme.brightness == Brightness.dark;

    return InputDecorationTheme(
      filled: true,
      fillColor: isDark
          ? AppColors
                .surfaceElevation1 // Use elevation color for inputs
          : colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(25), // Match button radius
        borderSide: isDark
            ? BorderSide(color: AppColors.cardBorderDark.withValues(alpha: 0.2))
            : BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(25),
        borderSide: isDark
            ? BorderSide(color: AppColors.cardBorderDark.withValues(alpha: 0.2))
            : BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(25),
        borderSide: BorderSide(
          color: AppColors.primary,
          width: 2,
        ), // Yellow focus
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(25),
        borderSide: BorderSide(color: colorScheme.error, width: 2),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      hintStyle: AppTypography.textTheme.bodyMedium?.copyWith(
        color: colorScheme.onSurface.withValues(alpha: 0.6),
      ),
    );
  }

  static ProgressIndicatorThemeData _progressIndicatorTheme(
    ColorScheme colorScheme,
  ) {
    return ProgressIndicatorThemeData(
      color: colorScheme.primary,
      linearTrackColor: colorScheme.surfaceContainerHighest,
      circularTrackColor: colorScheme.surfaceContainerHighest,
    );
  }

  static ChipThemeData _chipTheme(ColorScheme colorScheme) {
    final isDark = colorScheme.brightness == Brightness.dark;

    return ChipThemeData(
      backgroundColor: colorScheme.surfaceContainerHighest,
      selectedColor: colorScheme.primaryContainer,
      labelStyle: AppTypography.textTheme.labelMedium,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isDark
            ? BorderSide.none
            : BorderSide(
                color: AppColors.cardBorderLight,
                width: 0.5,
              ), // Add border for light theme visibility
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
    );
  }

  static DividerThemeData _dividerTheme(ColorScheme colorScheme) {
    return DividerThemeData(
      color: colorScheme.outlineVariant,
      thickness: 1,
      space: 1,
    );
  }
}
