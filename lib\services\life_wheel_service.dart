import 'package:firebase_auth/firebase_auth.dart';
import '../models/models.dart';
import 'firestore.dart';
import 'logging_service.dart';

/// Service for managing Life Wheel data operations
///
/// This service handles:
/// - Loading life area ratings and goals from UserProfile
/// - Saving updated life area data to UserProfile
/// - Providing life area definitions and structure
/// - Error handling for life wheel operations
class LifeWheelService {
  static const String _logTag = 'LifeWheelService';

  // Life areas in fixed order with descriptions and goals
  // Primary reference: https://productive.fish/blog/wheel-of-life/
  static const List<Map<String, dynamic>> lifeAreas = [
    {
      'key': 'personal_growth',
      'name': 'Personal Growth & Learning',
      'desc': 'Your learning habits, skill development, and mindset.',
      'goals': [
        'Read or learn weekly',
        'Take an online course',
        'Practice a new skill',
        'Find a mentor or coach',
        'Establish a daily learning habit',
      ],
    },
    {
      'key': 'finances',
      'name': 'Finances & Money Management',
      'desc': 'Earning, saving, budgeting, debt, and investments.',
      'goals': [
        'Create a monthly budget',
        'Track expenses weekly',
        'Build an emergency fund',
        'Pay down debt',
        'Start or increase investing',
      ],
    },
    {
      'key': 'health',
      'name': 'Health & Wellness',
      'desc': 'Physical and mental wellbeing: fitness, nutrition, rest.',
      'goals': [
        'Exercise 3x per week',
        'Improve daily nutrition',
        'Sleep 7-10 hours nightly',
        'Reduce stress (meditation/breathing)',
        'Schedule regular checkups',
      ],
    },
    {
      'key': 'romance',
      'name': 'Romance & Intimate Relationships',
      'desc': 'Quality of your love life and intimate connection.',
      'goals': [
        'Plan weekly quality time',
        'Improve communication skills',
        'Express appreciation daily',
        'Align expectations & boundaries',
        'Consider couples counseling',
      ],
    },
    {
      'key': 'family_friends',
      'name': 'Family & Friends',
      'desc': 'Support network, connection, and meaningful time.',
      'goals': [
        'Schedule regular catch-ups',
        'Strengthen key relationships',
        'Set healthier boundaries',
        'Be more present & attentive',
        'Expand social circle intentionally',
      ],
    },
    {
      'key': 'career',
      'name': 'Career & Professional Life',
      'desc': 'Work satisfaction, growth, alignment, and opportunities.',
      'goals': [
        'Clarify career direction',
        'Improve key professional skills',
        'Network intentionally',
        'Update resume/portfolio',
        'Pursue promotion or new role',
      ],
    },
    {
      'key': 'fun',
      'name': 'Fun & Recreation',
      'desc': 'Play, hobbies, joy, and restorative leisure.',
      'goals': [
        'Plan weekly leisure time',
        'Start or revive a hobby',
        'Take short trips/outings',
        'Reduce screen time for play',
        'Join a local club/activity',
      ],
    },
    {
      'key': 'environment',
      'name': 'Physical Environment & Home',
      'desc': 'Home and surroundings: comfort, order, and aesthetics.',
      'goals': [
        'Declutter key spaces',
        'Create a calming workspace',
        'Establish cleaning routines',
        'Upgrade organization systems',
        'Improve one room/area',
      ],
    },
  ];

  /// Load life wheel data from user's profile
  static Future<Map<String, dynamic>?> loadLifeWheelData(String userId) async {
    try {
      LoggingService.instance.logInfo(
        _logTag,
        'Loading life wheel data for user: $userId',
      );

      final userProfile = await FirestoreService.getUserProfile(userId);
      if (userProfile?.preferences == null) {
        LoggingService.instance.logInfo(
          _logTag,
          'No preferences found for user: $userId',
        );
        return null;
      }

      final lifeWheelData =
          userProfile!.preferences!['lifeWheel'] as Map<String, dynamic>?;
      if (lifeWheelData == null) {
        LoggingService.instance.logInfo(
          _logTag,
          'No life wheel data found for user: $userId',
        );
        return null;
      }

      LoggingService.instance.logInfo(
        _logTag,
        'Successfully loaded life wheel data for user: $userId',
      );

      return lifeWheelData;
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to load life wheel data for user: $userId',
      );
      rethrow;
    }
  }

  /// Save life wheel data to user's profile
  static Future<void> saveLifeWheelData({
    required String userId,
    required Map<String, int> areaRatings,
    required Map<String, List<String>> areaGoals,
  }) async {
    try {
      LoggingService.instance.logInfo(
        _logTag,
        'Saving life wheel data for user: $userId',
      );

      // Load current profile
      var userProfile = await FirestoreService.getUserProfile(userId);
      if (userProfile == null) {
        throw Exception('User profile not found for user: $userId');
      }

      // Prepare life wheel data structure
      final areas = lifeAreas
          .map(
            (a) => {
              'key': a['key'],
              'name': a['name'],
              'rating': areaRatings[a['key']] ?? 0,
              'goals': areaGoals[a['key']] ?? <String>[],
            },
          )
          .toList();

      // Update preferences with life wheel data
      final prefs = Map<String, dynamic>.from(userProfile.preferences ?? {});
      prefs['lifeWheel'] = {
        'areas': areas,
        'lastUpdated': DateTime.now().toIso8601String(),
        'source': 'life_wheel_page_v1',
      };

      // Create updated profile
      final updatedProfile = userProfile.copyWith(
        preferences: prefs,
        interactionHistory: userProfile.interactionHistory.copyWith(
          lastUpdated: DateTime.now(),
        ),
      );

      // Save to Firestore
      await FirestoreService.createOrUpdateUserProfile(updatedProfile);

      LoggingService.instance.logInfo(
        _logTag,
        'Successfully saved life wheel data for user: $userId',
      );
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to save life wheel data for user: $userId',
      );
      rethrow;
    }
  }

  /// Extract ratings and goals from life wheel data
  static Map<String, dynamic> parseLifeWheelData(
    Map<String, dynamic>? lifeWheelData,
  ) {
    final Map<String, int> ratings = {};
    final Map<String, List<String>> goals = {};

    if (lifeWheelData != null && lifeWheelData['areas'] != null) {
      final areas = lifeWheelData['areas'] as List<dynamic>;

      for (final area in areas) {
        if (area is Map<String, dynamic>) {
          final key = area['key'] as String?;
          if (key != null) {
            ratings[key] = (area['rating'] as num?)?.toInt() ?? 0;
            goals[key] = List<String>.from(area['goals'] as List? ?? []);
          }
        }
      }
    }

    return {'ratings': ratings, 'goals': goals};
  }

  /// Get current user's life wheel data
  static Future<Map<String, dynamic>?> getCurrentUserLifeWheelData() async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      throw Exception('No authenticated user found');
    }

    return await loadLifeWheelData(currentUser.uid);
  }

  /// Save current user's life wheel data
  static Future<void> saveCurrentUserLifeWheelData({
    required Map<String, int> areaRatings,
    required Map<String, List<String>> areaGoals,
  }) async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      throw Exception('No authenticated user found');
    }

    await saveLifeWheelData(
      userId: currentUser.uid,
      areaRatings: areaRatings,
      areaGoals: areaGoals,
    );
  }
}
