import 'package:flutter/material.dart';
import '../../widgets/onboarding/onboarding_step_base.dart';
import '../../widgets/life_wheel/life_wheel_widgets.dart';

class LifeAreaGoalsStep extends StatefulWidget {
  final String areaName;
  final List<String> goalOptions; // without the satisfied option
  final void Function(List<String> goals) onGoalsChanged;
  final VoidCallback? onBack;
  final VoidCallback? onSkip;
  final VoidCallback? onContinue;
  final List<String>? initialGoals;
  final int progressCurrent;
  final int progressTotal;

  const LifeAreaGoalsStep({
    super.key,
    required this.areaName,
    required this.goalOptions,
    required this.onGoalsChanged,
    this.onBack,
    this.onSkip,
    this.onContinue,
    this.initialGoals,
    required this.progressCurrent,
    required this.progressTotal,
  });

  @override
  State<LifeAreaGoalsStep> createState() => _LifeAreaGoalsStepState();
}

class _LifeAreaGoalsStepState extends State<LifeAreaGoalsStep> {
  late List<String> _selected;

  @override
  void initState() {
    super.initState();
    _selected = List.from(widget.initialGoals ?? <String>[]);
  }

  bool get _canContinue => _selected.isNotEmpty;

  @override
  Widget build(BuildContext context) {
    return OnboardingStepBase(
      currentStep: widget.progressCurrent,
      totalSteps: widget.progressTotal,
      title: widget.areaName,
      subtitle: 'Choose goals to improve this area',
      onBack: widget.onBack,
      onSkip: null,
      onContinue: _canContinue ? widget.onContinue : null,
      canContinue: _canContinue,
      child: LifeAreaGoalsWidget(
        areaName: widget.areaName,
        goalOptions: widget.goalOptions,
        initialGoals: _selected,
        onGoalsChanged: (goals) {
          setState(() {
            _selected = goals;
          });
          widget.onGoalsChanged(goals);
        },
        showAreaName: false, // Already shown in OnboardingStepBase
      ),
    );
  }
}
