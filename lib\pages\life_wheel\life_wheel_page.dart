import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/models.dart';
import '../../services/life_wheel_service.dart';
import '../../services/firestore.dart';
import '../../theme/theme.dart';
import '../../widgets/life_wheel/life_wheel_widgets.dart';
import '../../widgets/primary_action_button.dart';

/// Life Wheel page that allows users to view and update their life area ratings and goals
///
/// This page provides:
/// - Display of current life area ratings and goals from UserProfile
/// - Ability to modify both ratings (1-10 scale) and goals (text input)
/// - Save functionality that persists changes to Firestore
/// - Consistent UI patterns using Provider for state management
class LifeWheelPage extends StatefulWidget {
  const LifeWheelPage({super.key});

  @override
  State<LifeWheelPage> createState() => _LifeWheelPageState();
}

class _LifeWheelPageState extends State<LifeWheelPage> {
  bool _isLoading = true;
  bool _isSaving = false;
  String? _errorMessage;

  // Life wheel data
  final Map<String, int> _areaRatings = {};
  final Map<String, List<String>> _areaGoals = {};

  // Track if data has been modified
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _loadLifeWheelData();
  }

  Future<void> _loadLifeWheelData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final lifeWheelData =
          await LifeWheelService.getCurrentUserLifeWheelData();
      final parsedData = LifeWheelService.parseLifeWheelData(lifeWheelData);

      setState(() {
        _areaRatings.clear();
        _areaGoals.clear();

        final ratings = parsedData['ratings'] as Map<String, int>;
        final goals = parsedData['goals'] as Map<String, List<String>>;

        _areaRatings.addAll(ratings);
        _areaGoals.addAll(goals);

        _isLoading = false;
        _hasChanges = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load life wheel data: ${e.toString()}';
      });
    }
  }

  Future<void> _saveLifeWheelData() async {
    try {
      setState(() {
        _isSaving = true;
        _errorMessage = null;
      });

      await LifeWheelService.saveCurrentUserLifeWheelData(
        areaRatings: _areaRatings,
        areaGoals: _areaGoals,
      );

      setState(() {
        _isSaving = false;
        _hasChanges = false;
      });

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Life wheel data saved successfully!'),
            backgroundColor: AppColors.accent,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isSaving = false;
        _errorMessage = 'Failed to save life wheel data: ${e.toString()}';
      });
    }
  }

  void _onRatingChanged(String areaKey, int rating) {
    setState(() {
      _areaRatings[areaKey] = rating;
      _hasChanges = true;
    });
  }

  void _onGoalsChanged(String areaKey, List<String> goals) {
    setState(() {
      _areaGoals[areaKey] = goals;
      _hasChanges = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Life Wheel')),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Padding(
          padding: AppDimensions.paddingL,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: context.colorScheme.error,
              ),
              const SizedBox(height: 16),
              SelectableText.rich(
                TextSpan(
                  children: [
                    const TextSpan(
                      text: 'Error: ',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    TextSpan(text: _errorMessage!),
                  ],
                ),
                style: TextStyle(
                  color: context.colorScheme.error,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              PrimaryActionButton(
                text: 'Retry',
                onPressed: _loadLifeWheelData,
                icon: Icons.refresh,
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      children: [
        // Header section
        Container(
          width: double.infinity,
          padding: AppDimensions.paddingL,
          decoration: BoxDecoration(
            color: context.colorScheme.surface,
            border: Border(
              bottom: BorderSide(
                color: context.colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Review and update your life area ratings and goals. '
                'This helps you to prioritize the areas you want to focus on and your AI coach provide more personalized guidance.',
                style: context.textTheme.bodyLarge?.copyWith(
                  color: context.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),

        // Content
        Expanded(child: _buildLifeAreasContent()),

        // Save button
        if (_hasChanges) _buildSaveButton(),
      ],
    );
  }

  Widget _buildLifeAreasContent() {
    return ListView.separated(
      padding: AppDimensions.paddingL,
      itemCount: LifeWheelService.lifeAreas.length,
      separatorBuilder: (context, index) => const SizedBox(height: 32),
      itemBuilder: (context, index) {
        final area = LifeWheelService.lifeAreas[index];
        final areaKey = area['key'] as String;
        final areaName = area['name'] as String;
        final areaDesc = area['desc'] as String;
        final goalOptions = List<String>.from(area['goals'] as List);

        return _buildLifeAreaSection(
          areaKey: areaKey,
          areaName: areaName,
          areaDescription: areaDesc,
          goalOptions: goalOptions,
        );
      },
    );
  }

  Widget _buildLifeAreaSection({
    required String areaKey,
    required String areaName,
    required String areaDescription,
    required List<String> goalOptions,
  }) {
    return Container(
      padding: AppDimensions.paddingL,
      decoration: BoxDecoration(
        color: context.colorScheme.surface,
        borderRadius: AppDimensions.borderRadiusL,
        border: Border.all(
          color: context.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Rating section
          LifeAreaRatingWidget(
            areaName: areaName,
            areaDescription: areaDescription,
            initialRating: _areaRatings[areaKey],
            onRatingChanged: (rating) => _onRatingChanged(areaKey, rating),
          ),

          const SizedBox(height: 32),

          // Goals section
          LifeAreaGoalsWidget(
            areaName: areaName,
            goalOptions: goalOptions,
            initialGoals: _areaGoals[areaKey],
            onGoalsChanged: (goals) => _onGoalsChanged(areaKey, goals),
            showAreaName: false, // Already shown in rating widget
          ),
        ],
      ),
    );
  }

  Widget _buildSaveButton() {
    return Container(
      padding: AppDimensions.paddingL,
      decoration: BoxDecoration(
        color: context.colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: context.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: SafeArea(
        child: PrimaryActionButton(
          text: 'Save Changes',
          loadingText: 'Saving...',
          onPressed: _isSaving ? null : _saveLifeWheelData,
          isLoading: _isSaving,
          icon: Icons.save,
        ),
      ),
    );
  }
}
