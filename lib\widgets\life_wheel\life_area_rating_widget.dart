import 'package:flutter/material.dart';
import '../../theme/theme.dart';

/// A reusable widget for rating life areas on a 1-10 scale
///
/// This widget provides:
/// - Visual slider with gradient background
/// - Rating display and labels
/// - Consistent styling with onboarding flow
/// - Callback for rating changes
class LifeAreaRatingWidget extends StatefulWidget {
  final String areaName;
  final String areaDescription;
  final int? initialRating;
  final void Function(int rating) onRatingChanged;
  final bool showDescription;

  const LifeAreaRatingWidget({
    super.key,
    required this.areaName,
    required this.areaDescription,
    this.initialRating,
    required this.onRatingChanged,
    this.showDescription = true,
  });

  @override
  State<LifeAreaRatingWidget> createState() => _LifeAreaRatingWidgetState();
}

class _LifeAreaRatingWidgetState extends State<LifeAreaRatingWidget> {
  int? _rating;

  @override
  void initState() {
    super.initState();
    _rating = widget.initialRating;
  }

  @override
  void didUpdateWidget(LifeAreaRatingWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initialRating != oldWidget.initialRating) {
      _rating = widget.initialRating;
    }
  }

  void _selectRating(int value) {
    setState(() => _rating = value);
    widget.onRatingChanged(value);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Area name
        Text(
          widget.areaName,
          style: context.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),

        if (widget.showDescription) ...[
          const SizedBox(height: AppDimensions.spacingS),
          // Area description
          Text(
            widget.areaDescription,
            style: context.textTheme.bodyLarge?.copyWith(
              color: context.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],

        const SizedBox(height: AppDimensions.spacingL),

        const SizedBox(height: AppDimensions.spacingM),

        // Slider with gradient background
        Stack(
          children: [
            // Gradient background
            Container(
              height: 16,
              decoration: BoxDecoration(
                borderRadius: AppDimensions.borderRadiusS,
                gradient: const LinearGradient(
                  colors: [
                    Color(0xFFFF6B6B), // Red for low scores
                    Color(0xFFFFE66D), // Yellow for medium scores
                    Color(0xFF4ECDC4), // Teal for high scores
                  ],
                ),
              ),
            ),

            // Transparent track slider over gradient
            SliderTheme(
              data: SliderTheme.of(context).copyWith(
                trackHeight: 16,
                inactiveTrackColor: Colors.transparent,
                activeTrackColor: Colors.transparent,
                thumbColor: context.colorScheme.surface,
                overlayColor: context.colorScheme.primary.withValues(
                  alpha: 0.2,
                ),
                thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 12),
              ),
              child: Slider(
                min: 1,
                max: 10,
                divisions: 9,
                value: (_rating ?? 5).toDouble(),
                label: _rating?.toString(),
                onChanged: (v) => _selectRating(v.round()),
              ),
            ),
          ],
        ),

        // Rating labels
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '1',
              style: context.textTheme.bodySmall?.copyWith(
                color: context.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            Text(
              '10',
              style: context.textTheme.bodySmall?.copyWith(
                color: context.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
